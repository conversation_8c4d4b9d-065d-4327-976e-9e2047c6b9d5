"""
Test module for Ollama server integration.

This module contains tests for interacting with a local Ollama server.
"""
import json
import time
import pytest
import requests
from requests.exceptions import RequestException

# Constants
# OLLAMA_BASE_URLS = ["http://localhost:11434", "http://**************:11434"]
# MODEL_NAME = "qwen3:14b"

# def get_available_ollama_url():
#     """Return the first available Ollama base URL."""
#     for url in OLLAMA_BASE_URLS:
#         try:
#             response = requests.get(f"{url}/api/tags", timeout=3)
#             if response.status_code == 200:
#                 return url
#         except requests.RequestException:
#             continue
#     return OLLAMA_BASE_URLS[0]  # fallback to localhost

# OLLAMA_BASE_URL = get_available_ollama_url()
OLLAMA_BASE_URL = "http://**************:11434"
TEST_TIMEOUT = 300  # seconds - LLMs are slow when run locally through Ollama on a laptop

# Test data
TEST_PROMPTS = [
    "What is the capital of France?",
    "Explain quantum computing in simple terms.",
    "Write a Python function to calculate factorial."
]

# Fixtures
@pytest.fixture(scope="module")
def ollama_available() -> bool:
    """Check if Ollama server is available."""
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        return response.status_code == 200
    except (RequestException, ConnectionError):
        return False

@pytest.fixture(scope="module")
def model_available(ollama_available) -> bool:
    """Check if the required model is available."""
    if not ollama_available:
        return False
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=10)
        models = [model.get("name") for model in response.json().get("models", [])]
        return MODEL_NAME in models
    except (RequestException, KeyError, json.JSONDecodeError):
        return False

# Skip decorator that will be configured in conftest.py
skip_if_ollama_unavailable = pytest.mark.skip(
    reason="Ollama tests disabled. Use --run-ollama-tests to enable."
)

# Tests
class TestOllamaIntegration:
    """Test suite for Ollama server integration."""

    @pytest.mark.dependency(name="server_available")
    def test_ollama_server_running(self, ollama_available):
        """Test if Ollama server is running and accessible."""
        assert ollama_available, f"Ollama server is not running or accessible at the expected URL ({OLLAMA_BASE_URL})"

    @pytest.mark.dependency(depends=["server_available"])
    def test_model_available(self, model_available):
        """Test if the required model is available."""
        assert model_available, f"Model {MODEL_NAME} is not available"

    @pytest.mark.dependency(depends=["server_available"])
    def test_generate_completion(self, model_available):
        """Test generating a completion from the model."""
        if not model_available:
            pytest.skip(f"Model {MODEL_NAME} is not available")

        prompt = TEST_PROMPTS[0]
        payload = {
            "model": MODEL_NAME,
            "prompt": prompt,
            "stream": False
        }
        
        try:
            response = requests.post(
                f"{OLLAMA_BASE_URL}/api/generate",
                json=payload,
                timeout=TEST_TIMEOUT
            )
            response.raise_for_status()
            response_data = response.json()
            
            assert "response" in response_data, "No response in completion"
            assert isinstance(response_data["response"], str), "Response should be a string"
            assert len(response_data["response"].strip()) > 0, "Empty response from model"
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Request failed: {str(e)}")
        except json.JSONDecodeError:
            pytest.fail("Failed to decode JSON response")

    @pytest.mark.parametrize("prompt", TEST_PROMPTS)
    @pytest.mark.dependency(depends=["server_available"])
    def test_multiple_prompts(self, prompt: str, model_available):
        """Test the model with multiple different prompts."""
        if not model_available:
            pytest.skip(f"Model {MODEL_NAME} is not available")

        payload = {
            "model": MODEL_NAME,
            "prompt": prompt,
            "stream": False
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{OLLAMA_BASE_URL}/api/generate",
                json=payload,
                timeout=TEST_TIMEOUT
            )
            response.raise_for_status()
            response_data = response.json()
            
            assert "response" in response_data, "No response in completion"
            assert isinstance(response_data["response"], str), "Response should be a string"
            assert len(response_data["response"].strip()) > 0, "Empty response from model"
            
            # Log test duration
            duration = time.time() - start_time
            print(f"\nPrompt: {prompt[:50]}... - Response time: {duration:.2f}s")
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Request failed for prompt '{prompt}': {str(e)}")
        except json.JSONDecodeError:
            pytest.fail(f"Failed to decode JSON response for prompt: {prompt}")

def pytest_addoption(parser):
    """Add custom command line options for pytest."""
    parser.addoption(
        "--run-ollama-tests",
        action="store_true",
        default=False,
        help="Run tests that require Ollama server"
    )

def pytest_configure(config):
    """Register custom markers."""
    config.addinivalue_line(
        "markers",
        "ollama: mark test as requiring Ollama server"
    )
